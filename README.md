# 荧光强度检测方法对比分析系统

## 系统概述

本系统用于对比实验组和对照组荧光强度检测性能的完整分析工具。系统能够：

1. **生成模拟数据**：基于4个理论荧光强度水平（8000、32000、128000、256000）生成实验组和对照组的检测数据
2. **相关性分析**：评估实验组、对照组与理论值的相关性
3. **一致性分析**：进行CV分析和Bland-Altman一致性评估
4. **统计学检验**：包括配对t检验、Wilcoxon检验等
5. **数据可视化**：生成符合Nature期刊标准的高质量图表

## 环境要求

### Python版本
- Python 3.7 或更高版本

### 依赖包
```bash
pip install numpy pandas matplotlib seaborn scipy
```

或者使用conda：
```bash
conda install numpy pandas matplotlib seaborn scipy
```

## 安装步骤

### 1. 检查Python环境
```bash
python --version
# 或
py --version
```

### 2. 安装依赖包
如果您的系统中pip不可用，请先安装pip：

**Windows系统：**
1. 下载get-pip.py：https://bootstrap.pypa.io/get-pip.py
2. 运行：`python get-pip.py`

**或者使用conda（推荐）：**
1. 下载并安装Anaconda：https://www.anaconda.com/products/distribution
2. 使用conda安装依赖：`conda install numpy pandas matplotlib seaborn scipy`

### 3. 验证安装
```python
import numpy, pandas, matplotlib, seaborn, scipy
print("所有依赖包安装成功！")
```

## 使用方法

### 快速开始
```python
# 导入主程序
from fluorescence_analysis import FluorescenceAnalyzer

# 创建分析器实例
analyzer = FluorescenceAnalyzer()

# 运行完整分析
analyzer.run_complete_analysis()
```

### 分步执行
```python
# 1. 生成数据
analyzer = FluorescenceAnalyzer()
data = analyzer.generate_data()

# 2. 进行分析
analyzer.correlation_analysis()
analyzer.consistency_analysis()
analyzer.statistical_tests()

# 3. 生成图表
analyzer.create_output_directory()
analyzer.save_data()
analyzer.plot_scatter_comparison()
analyzer.plot_boxplot_comparison()
analyzer.plot_bland_altman()
analyzer.plot_correlation_heatmap()
analyzer.plot_efficiency_comparison()
```

## 输出文件

所有结果将保存在 `outputs/时间戳/` 目录下：

### CSV数据文件
- `荧光强度检测原始数据.csv`：包含所有生成的原始数据
- `统计分析结果.csv`：包含所有统计分析结果

### PNG图像文件
- `实际值vs理论值散点图对比.png`：散点图比较
- `荧光强度分布箱线图.png`：分布比较
- `Bland-Altman一致性分析图.png`：一致性分析
- `相关性热图.png`：相关性矩阵
- `检测效率对比柱状图.png`：效率对比

## 数据说明

### 实验设计
- **理论值**：8000、32000、128000、256000（4个浓度水平）
- **实验组**：检测效率50-80%，随机波动3-5%
- **对照组**：检测效率30-60%，随机波动3-8%
- **重复次数**：每组3个平行样

### 命名规则
- `F_1A1`：第1个浓度水平，实验组（A），第1个平行样
- `F_1B1`：第1个浓度水平，对照组（B），第1个平行样

## 分析内容

### 1. 相关性分析
- Pearson相关系数
- Spearman相关系数
- 显著性检验

### 2. 一致性分析
- 变异系数（CV）
- Bland-Altman分析
- 95%一致性界限

### 3. 统计学检验
- 配对t检验
- Wilcoxon符号秩检验
- 检测效率比较

## 图表规范

所有图表遵循Nature期刊标准：
- **字体**：Arial, 12pt
- **尺寸**：8×6英寸
- **分辨率**：300 DPI
- **配色**：莫兰迪配色方案
- **格式**：PNG

## 测试

运行测试脚本验证系统功能：
```bash
python test_fluorescence_analysis.py
```

测试将验证：
- 数据生成正确性
- 分析功能完整性
- 输出文件生成
- 图表质量

## 故障排除

### 常见问题

1. **ImportError: No module named 'xxx'**
   - 解决：安装缺失的依赖包 `pip install xxx`

2. **Python命令不识别**
   - 解决：检查Python是否正确安装并添加到PATH

3. **图片无法显示**
   - 解决：确保matplotlib后端设置正确，使用非交互式后端

4. **中文显示乱码**
   - 解决：确保系统支持UTF-8编码

### 技术支持

如遇到问题，请检查：
1. Python版本是否符合要求
2. 所有依赖包是否正确安装
3. 文件权限是否允许写入
4. 磁盘空间是否充足

## 许可证

本项目仅供学术研究使用。
