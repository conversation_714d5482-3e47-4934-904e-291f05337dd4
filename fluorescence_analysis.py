#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
荧光强度检测方法对比分析系统
Fluorescence Intensity Detection Method Comparison Analysis System

作者: AI Assistant
日期: 2025-08-28
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import pearsonr, spearmanr, ttest_rel, wilcoxon
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib参数以符合Nature期刊标准
plt.rcParams.update({
    'font.family': 'Arial',
    'font.size': 12,
    'figure.figsize': (8, 6),
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'savefig.bbox': 'tight',
    'axes.linewidth': 1,
    'axes.spines.top': False,
    'axes.spines.right': False
})

# Nature期刊莫兰迪配色方案
NATURE_COLORS = {
    'experimental': '#7B68A6',  # 紫灰色
    'control': '#A6A6A6',       # 中性灰
    'theoretical': '#2E8B57',   # 海绿色
    'accent1': '#CD853F',       # 秘鲁色
    'accent2': '#8FBC8F',       # 暗海绿
    'accent3': '#DDA0DD'        # 梅红色
}

class FluorescenceAnalyzer:
    """荧光强度检测分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.theoretical_values = [8000, 32000, 128000, 256000]
        self.data = None
        self.results = {}
        self.output_dir = None
        
    def generate_data(self):
        """生成模拟荧光强度数据"""
        print("正在生成模拟数据...")
        
        data_list = []
        
        for i, theoretical in enumerate(self.theoretical_values, 1):
            # 对照组：30-60%效率，3-8%随机波动
            control_efficiency = np.random.uniform(0.30, 0.60)
            control_base = theoretical * control_efficiency
            control_values = []
            for j in range(3):
                fluctuation = np.random.uniform(-0.08, 0.08)
                value = control_base * (1 + fluctuation)
                control_values.append(max(0, value))  # 确保非负值
            
            # 实验组：50-80%效率，3-5%随机波动
            exp_efficiency = np.random.uniform(0.50, 0.80)
            exp_base = theoretical * exp_efficiency
            exp_values = []
            for j in range(3):
                fluctuation = np.random.uniform(-0.05, 0.05)
                value = exp_base * (1 + fluctuation)
                exp_values.append(max(0, value))  # 确保非负值
            
            # 构建数据行
            row = {
                'Concentration_Level': i,
                'Theoretical_Value': theoretical,
                f'F_{i}A1': exp_values[0],
                f'F_{i}A2': exp_values[1],
                f'F_{i}A3': exp_values[2],
                f'F_{i}B1': control_values[0],
                f'F_{i}B2': control_values[1],
                f'F_{i}B3': control_values[2],
                'Exp_Mean': np.mean(exp_values),
                'Control_Mean': np.mean(control_values),
                'Exp_CV': np.std(exp_values) / np.mean(exp_values) * 100,
                'Control_CV': np.std(control_values) / np.mean(control_values) * 100,
                'Exp_Efficiency': exp_efficiency * 100,
                'Control_Efficiency': control_efficiency * 100
            }
            data_list.append(row)
        
        self.data = pd.DataFrame(data_list)
        print("数据生成完成！")
        return self.data
    
    def correlation_analysis(self):
        """相关性分析"""
        print("正在进行相关性分析...")
        
        # 实验组与理论值的相关性
        exp_pearson_r, exp_pearson_p = pearsonr(self.data['Theoretical_Value'], 
                                               self.data['Exp_Mean'])
        exp_spearman_r, exp_spearman_p = spearmanr(self.data['Theoretical_Value'], 
                                                 self.data['Exp_Mean'])
        
        # 对照组与理论值的相关性
        ctrl_pearson_r, ctrl_pearson_p = pearsonr(self.data['Theoretical_Value'], 
                                                 self.data['Control_Mean'])
        ctrl_spearman_r, ctrl_spearman_p = spearmanr(self.data['Theoretical_Value'], 
                                                   self.data['Control_Mean'])
        
        # 实验组与对照组的相关性
        inter_pearson_r, inter_pearson_p = pearsonr(self.data['Exp_Mean'], 
                                                   self.data['Control_Mean'])
        inter_spearman_r, inter_spearman_p = spearmanr(self.data['Exp_Mean'], 
                                                      self.data['Control_Mean'])
        
        self.results['correlation'] = {
            'exp_vs_theoretical': {
                'pearson_r': exp_pearson_r, 'pearson_p': exp_pearson_p,
                'spearman_r': exp_spearman_r, 'spearman_p': exp_spearman_p
            },
            'control_vs_theoretical': {
                'pearson_r': ctrl_pearson_r, 'pearson_p': ctrl_pearson_p,
                'spearman_r': ctrl_spearman_r, 'spearman_p': ctrl_spearman_p
            },
            'exp_vs_control': {
                'pearson_r': inter_pearson_r, 'pearson_p': inter_pearson_p,
                'spearman_r': inter_spearman_r, 'spearman_p': inter_spearman_p
            }
        }
        
        print("相关性分析完成！")
        return self.results['correlation']
    
    def consistency_analysis(self):
        """一致性分析"""
        print("正在进行一致性分析...")
        
        # CV分析已在数据生成时计算
        mean_exp_cv = self.data['Exp_CV'].mean()
        mean_control_cv = self.data['Control_CV'].mean()
        
        # Bland-Altman分析数据准备
        differences = self.data['Exp_Mean'] - self.data['Control_Mean']
        means = (self.data['Exp_Mean'] + self.data['Control_Mean']) / 2
        mean_diff = differences.mean()
        std_diff = differences.std()
        
        self.results['consistency'] = {
            'mean_exp_cv': mean_exp_cv,
            'mean_control_cv': mean_control_cv,
            'bland_altman': {
                'mean_difference': mean_diff,
                'std_difference': std_diff,
                'upper_limit': mean_diff + 1.96 * std_diff,
                'lower_limit': mean_diff - 1.96 * std_diff
            }
        }
        
        print("一致性分析完成！")
        return self.results['consistency']
    
    def statistical_tests(self):
        """统计学检验"""
        print("正在进行统计学检验...")
        
        # 配对t检验
        t_stat, t_p = ttest_rel(self.data['Exp_Mean'], self.data['Control_Mean'])
        
        # Wilcoxon符号秩检验
        w_stat, w_p = wilcoxon(self.data['Exp_Mean'], self.data['Control_Mean'])
        
        # 检测效率比较
        eff_t_stat, eff_t_p = ttest_rel(self.data['Exp_Efficiency'], 
                                       self.data['Control_Efficiency'])
        
        self.results['statistical_tests'] = {
            'paired_t_test': {'statistic': t_stat, 'p_value': t_p},
            'wilcoxon_test': {'statistic': w_stat, 'p_value': w_p},
            'efficiency_t_test': {'statistic': eff_t_stat, 'p_value': eff_t_p}
        }
        
        print("统计学检验完成！")
        return self.results['statistical_tests']

    def create_output_directory(self):
        """创建输出目录"""
        timestamp = datetime.now().strftime("%y%m%d%H%M")
        self.output_dir = f"outputs/{timestamp}"
        os.makedirs(self.output_dir, exist_ok=True)
        print(f"输出目录已创建: {self.output_dir}")
        return self.output_dir

    def save_data(self):
        """保存数据到CSV文件"""
        if self.output_dir is None:
            self.create_output_directory()

        # 保存原始数据
        data_file = os.path.join(self.output_dir, "荧光强度检测原始数据.csv")
        self.data.to_csv(data_file, index=False, encoding='utf-8-sig')

        # 保存分析结果
        results_data = []

        # 相关性结果
        corr = self.results['correlation']
        results_data.extend([
            ['相关性分析', '实验组vs理论值', 'Pearson相关系数', corr['exp_vs_theoretical']['pearson_r']],
            ['相关性分析', '实验组vs理论值', 'Pearson p值', corr['exp_vs_theoretical']['pearson_p']],
            ['相关性分析', '对照组vs理论值', 'Pearson相关系数', corr['control_vs_theoretical']['pearson_r']],
            ['相关性分析', '对照组vs理论值', 'Pearson p值', corr['control_vs_theoretical']['pearson_p']],
            ['相关性分析', '实验组vs对照组', 'Pearson相关系数', corr['exp_vs_control']['pearson_r']],
            ['相关性分析', '实验组vs对照组', 'Pearson p值', corr['exp_vs_control']['pearson_p']]
        ])

        # 一致性结果
        consist = self.results['consistency']
        results_data.extend([
            ['一致性分析', '实验组', '平均CV(%)', consist['mean_exp_cv']],
            ['一致性分析', '对照组', '平均CV(%)', consist['mean_control_cv']],
            ['一致性分析', 'Bland-Altman', '平均差值', consist['bland_altman']['mean_difference']],
            ['一致性分析', 'Bland-Altman', '差值标准差', consist['bland_altman']['std_difference']]
        ])

        # 统计检验结果
        stats_test = self.results['statistical_tests']
        results_data.extend([
            ['统计检验', '配对t检验', 't统计量', stats_test['paired_t_test']['statistic']],
            ['统计检验', '配对t检验', 'p值', stats_test['paired_t_test']['p_value']],
            ['统计检验', 'Wilcoxon检验', 'W统计量', stats_test['wilcoxon_test']['statistic']],
            ['统计检验', 'Wilcoxon检验', 'p值', stats_test['wilcoxon_test']['p_value']],
            ['统计检验', '效率t检验', 't统计量', stats_test['efficiency_t_test']['statistic']],
            ['统计检验', '效率t检验', 'p值', stats_test['efficiency_t_test']['p_value']]
        ])

        results_df = pd.DataFrame(results_data,
                                columns=['分析类型', '比较组', '指标', '数值'])
        results_file = os.path.join(self.output_dir, "统计分析结果.csv")
        results_df.to_csv(results_file, index=False, encoding='utf-8-sig')

        print("数据文件保存完成！")

    def plot_scatter_comparison(self):
        """绘制散点图比较实际值vs理论值"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

        # 实验组散点图
        ax1.scatter(self.data['Theoretical_Value'], self.data['Exp_Mean'],
                   color=NATURE_COLORS['experimental'], s=100, alpha=0.7, label='实验组')
        ax1.plot([0, max(self.data['Theoretical_Value'])],
                [0, max(self.data['Theoretical_Value'])],
                'k--', alpha=0.5, label='理论线 (y=x)')

        # 添加拟合线
        z = np.polyfit(self.data['Theoretical_Value'], self.data['Exp_Mean'], 1)
        p = np.poly1d(z)
        ax1.plot(self.data['Theoretical_Value'], p(self.data['Theoretical_Value']),
                color=NATURE_COLORS['accent1'], linewidth=2, label='拟合线')

        ax1.set_xlabel('理论荧光强度')
        ax1.set_ylabel('实测荧光强度')
        ax1.set_title('实验组检测性能')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 对照组散点图
        ax2.scatter(self.data['Theoretical_Value'], self.data['Control_Mean'],
                   color=NATURE_COLORS['control'], s=100, alpha=0.7, label='对照组')
        ax2.plot([0, max(self.data['Theoretical_Value'])],
                [0, max(self.data['Theoretical_Value'])],
                'k--', alpha=0.5, label='理论线 (y=x)')

        # 添加拟合线
        z = np.polyfit(self.data['Theoretical_Value'], self.data['Control_Mean'], 1)
        p = np.poly1d(z)
        ax2.plot(self.data['Theoretical_Value'], p(self.data['Theoretical_Value']),
                color=NATURE_COLORS['accent1'], linewidth=2, label='拟合线')

        ax2.set_xlabel('理论荧光强度')
        ax2.set_ylabel('实测荧光强度')
        ax2.set_title('对照组检测性能')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, "实际值vs理论值散点图对比.png"))
        plt.close()

        print("散点图对比已保存！")

    def plot_boxplot_comparison(self):
        """绘制箱线图比较两组检测结果分布"""
        # 准备数据
        plot_data = []
        for i, row in self.data.iterrows():
            conc_level = f"浓度{row['Concentration_Level']}"
            plot_data.extend([
                {'浓度水平': conc_level, '检测方法': '实验组', '荧光强度': row['Exp_Mean']},
                {'浓度水平': conc_level, '检测方法': '对照组', '荧光强度': row['Control_Mean']}
            ])

        plot_df = pd.DataFrame(plot_data)

        plt.figure(figsize=(8, 6))
        sns.boxplot(data=plot_df, x='浓度水平', y='荧光强度', hue='检测方法',
                   palette=[NATURE_COLORS['experimental'], NATURE_COLORS['control']])
        plt.title('不同浓度水平下两种检测方法的荧光强度分布')
        plt.xlabel('浓度水平')
        plt.ylabel('荧光强度')
        plt.legend(title='检测方法')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, "荧光强度分布箱线图.png"))
        plt.close()

        print("箱线图已保存！")

    def plot_bland_altman(self):
        """绘制Bland-Altman一致性图"""
        differences = self.data['Exp_Mean'] - self.data['Control_Mean']
        means = (self.data['Exp_Mean'] + self.data['Control_Mean']) / 2

        mean_diff = differences.mean()
        std_diff = differences.std()
        upper_limit = mean_diff + 1.96 * std_diff
        lower_limit = mean_diff - 1.96 * std_diff

        plt.figure(figsize=(8, 6))
        plt.scatter(means, differences, color=NATURE_COLORS['experimental'],
                   s=100, alpha=0.7)

        # 添加参考线
        plt.axhline(mean_diff, color=NATURE_COLORS['theoretical'],
                   linestyle='-', linewidth=2, label=f'平均差值: {mean_diff:.1f}')
        plt.axhline(upper_limit, color=NATURE_COLORS['accent1'],
                   linestyle='--', linewidth=2, label=f'上限: {upper_limit:.1f}')
        plt.axhline(lower_limit, color=NATURE_COLORS['accent1'],
                   linestyle='--', linewidth=2, label=f'下限: {lower_limit:.1f}')
        plt.axhline(0, color='black', linestyle='-', alpha=0.3)

        plt.xlabel('两种方法的平均值')
        plt.ylabel('差值 (实验组 - 对照组)')
        plt.title('Bland-Altman一致性分析')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, "Bland-Altman一致性分析图.png"))
        plt.close()

        print("Bland-Altman图已保存！")

    def plot_correlation_heatmap(self):
        """绘制相关性热图"""
        # 准备相关性矩阵数据
        corr_data = self.data[['Theoretical_Value', 'Exp_Mean', 'Control_Mean']].copy()
        corr_data.columns = ['理论值', '实验组', '对照组']

        correlation_matrix = corr_data.corr()

        plt.figure(figsize=(8, 6))
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='RdYlBu_r',
                   center=0, square=True, linewidths=0.5, cbar_kws={"shrink": .8})
        plt.title('荧光强度检测相关性热图')
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, "相关性热图.png"))
        plt.close()

        print("相关性热图已保存！")

    def plot_efficiency_comparison(self):
        """绘制检测效率对比柱状图"""
        categories = [f'浓度{i}' for i in range(1, 5)]
        exp_eff = self.data['Exp_Efficiency'].values
        ctrl_eff = self.data['Control_Efficiency'].values

        x = np.arange(len(categories))
        width = 0.35

        plt.figure(figsize=(8, 6))
        bars1 = plt.bar(x - width/2, exp_eff, width, label='实验组',
                       color=NATURE_COLORS['experimental'], alpha=0.8)
        bars2 = plt.bar(x + width/2, ctrl_eff, width, label='对照组',
                       color=NATURE_COLORS['control'], alpha=0.8)

        # 添加数值标签
        for bar in bars1:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{height:.1f}%', ha='center', va='bottom')

        for bar in bars2:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{height:.1f}%', ha='center', va='bottom')

        plt.xlabel('浓度水平')
        plt.ylabel('检测效率 (%)')
        plt.title('不同浓度水平下的检测效率对比')
        plt.xticks(x, categories)
        plt.legend()
        plt.ylim(0, 100)
        plt.grid(True, alpha=0.3, axis='y')
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, "检测效率对比柱状图.png"))
        plt.close()

        print("检测效率对比图已保存！")

    def run_complete_analysis(self):
        """运行完整的分析流程"""
        print("=" * 60)
        print("荧光强度检测方法对比分析系统")
        print("=" * 60)

        # 创建输出目录
        self.create_output_directory()

        # 生成数据
        self.generate_data()

        # 进行各项分析
        self.correlation_analysis()
        self.consistency_analysis()
        self.statistical_tests()

        # 保存数据
        self.save_data()

        # 生成所有图表
        print("\n正在生成可视化图表...")
        self.plot_scatter_comparison()
        self.plot_boxplot_comparison()
        self.plot_bland_altman()
        self.plot_correlation_heatmap()
        self.plot_efficiency_comparison()

        # 打印分析结果摘要
        self.print_summary()

        print(f"\n分析完成！所有结果已保存到: {self.output_dir}")
        print("=" * 60)

    def print_summary(self):
        """打印分析结果摘要"""
        print("\n" + "=" * 40)
        print("分析结果摘要")
        print("=" * 40)

        # 相关性结果
        corr = self.results['correlation']
        print(f"\n【相关性分析】")
        print(f"实验组vs理论值: r={corr['exp_vs_theoretical']['pearson_r']:.3f}, "
              f"p={corr['exp_vs_theoretical']['pearson_p']:.3f}")
        print(f"对照组vs理论值: r={corr['control_vs_theoretical']['pearson_r']:.3f}, "
              f"p={corr['control_vs_theoretical']['pearson_p']:.3f}")
        print(f"实验组vs对照组: r={corr['exp_vs_control']['pearson_r']:.3f}, "
              f"p={corr['exp_vs_control']['pearson_p']:.3f}")

        # 一致性结果
        consist = self.results['consistency']
        print(f"\n【一致性分析】")
        print(f"实验组平均CV: {consist['mean_exp_cv']:.2f}%")
        print(f"对照组平均CV: {consist['mean_control_cv']:.2f}%")
        print(f"Bland-Altman平均差值: {consist['bland_altman']['mean_difference']:.1f}")

        # 统计检验结果
        stats_test = self.results['statistical_tests']
        print(f"\n【统计检验】")
        print(f"配对t检验: t={stats_test['paired_t_test']['statistic']:.3f}, "
              f"p={stats_test['paired_t_test']['p_value']:.3f}")
        print(f"Wilcoxon检验: W={stats_test['wilcoxon_test']['statistic']:.1f}, "
              f"p={stats_test['wilcoxon_test']['p_value']:.3f}")
        print(f"效率t检验: t={stats_test['efficiency_t_test']['statistic']:.3f}, "
              f"p={stats_test['efficiency_t_test']['p_value']:.3f}")

        # 检测效率摘要
        print(f"\n【检测效率摘要】")
        print(f"实验组平均效率: {self.data['Exp_Efficiency'].mean():.1f}%")
        print(f"对照组平均效率: {self.data['Control_Efficiency'].mean():.1f}%")
        print(f"效率提升: {self.data['Exp_Efficiency'].mean() - self.data['Control_Efficiency'].mean():.1f}%")


def main():
    """主函数"""
    # 创建分析器实例
    analyzer = FluorescenceAnalyzer()

    # 运行完整分析
    analyzer.run_complete_analysis()

    return analyzer


if __name__ == "__main__":
    # 运行主程序
    analyzer = main()
