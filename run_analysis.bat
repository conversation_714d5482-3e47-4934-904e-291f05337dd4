@echo off
echo 荧光强度检测分析系统启动器
echo ================================

echo 正在检测Python环境...

REM 尝试不同的Python命令
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo 使用python命令运行...
    python simple_demo.py
    goto :end
)

py --version >nul 2>&1
if %errorlevel% == 0 (
    echo 使用py命令运行...
    py simple_demo.py
    goto :end
)

python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo 使用python3命令运行...
    python3 simple_demo.py
    goto :end
)

echo 错误：未找到Python环境！
echo 请确保Python已正确安装并添加到PATH环境变量中。
echo.
echo 安装建议：
echo 1. 下载并安装Python: https://www.python.org/downloads/
echo 2. 或安装Anaconda: https://www.anaconda.com/products/distribution
echo 3. 确保在安装时勾选"Add Python to PATH"选项
echo.

:end
pause
