#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
荧光强度检测分析系统 - 简化演示版本
Simplified Demo Version of Fluorescence Analysis System

此版本仅使用Python内置库，用于演示核心功能
完整版本请使用 fluorescence_analysis.py（需要安装numpy, pandas, matplotlib等依赖）
"""

import random
import math
import csv
import os
from datetime import datetime

class SimpleFluorescenceDemo:
    """简化版荧光强度检测分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.theoretical_values = [8000, 32000, 128000, 256000]
        self.data = []
        self.output_dir = None
        
    def generate_data(self):
        """生成模拟荧光强度数据"""
        print("正在生成模拟数据...")
        
        for i, theoretical in enumerate(self.theoretical_values, 1):
            # 对照组：30-60%效率，3-8%随机波动
            control_efficiency = random.uniform(0.30, 0.60)
            control_base = theoretical * control_efficiency
            control_values = []
            for j in range(3):
                fluctuation = random.uniform(-0.08, 0.08)
                value = max(0, control_base * (1 + fluctuation))
                control_values.append(value)
            
            # 实验组：50-80%效率，3-5%随机波动
            exp_efficiency = random.uniform(0.50, 0.80)
            exp_base = theoretical * exp_efficiency
            exp_values = []
            for j in range(3):
                fluctuation = random.uniform(-0.05, 0.05)
                value = max(0, exp_base * (1 + fluctuation))
                exp_values.append(value)
            
            # 计算统计量
            exp_mean = sum(exp_values) / len(exp_values)
            control_mean = sum(control_values) / len(control_values)
            
            exp_std = math.sqrt(sum((x - exp_mean)**2 for x in exp_values) / len(exp_values))
            control_std = math.sqrt(sum((x - control_mean)**2 for x in control_values) / len(control_values))
            
            exp_cv = (exp_std / exp_mean) * 100 if exp_mean > 0 else 0
            control_cv = (control_std / control_mean) * 100 if control_mean > 0 else 0
            
            # 构建数据行
            row = {
                'Concentration_Level': i,
                'Theoretical_Value': theoretical,
                f'F_{i}A1': exp_values[0],
                f'F_{i}A2': exp_values[1],
                f'F_{i}A3': exp_values[2],
                f'F_{i}B1': control_values[0],
                f'F_{i}B2': control_values[1],
                f'F_{i}B3': control_values[2],
                'Exp_Mean': exp_mean,
                'Control_Mean': control_mean,
                'Exp_CV': exp_cv,
                'Control_CV': control_cv,
                'Exp_Efficiency': exp_efficiency * 100,
                'Control_Efficiency': control_efficiency * 100
            }
            self.data.append(row)
        
        print("数据生成完成！")
        return self.data
    
    def calculate_correlation(self, x_values, y_values):
        """计算Pearson相关系数"""
        n = len(x_values)
        if n == 0:
            return 0, 1
        
        # 计算均值
        mean_x = sum(x_values) / n
        mean_y = sum(y_values) / n
        
        # 计算相关系数
        numerator = sum((x_values[i] - mean_x) * (y_values[i] - mean_y) for i in range(n))
        sum_sq_x = sum((x_values[i] - mean_x) ** 2 for i in range(n))
        sum_sq_y = sum((y_values[i] - mean_y) ** 2 for i in range(n))
        
        denominator = math.sqrt(sum_sq_x * sum_sq_y)
        
        if denominator == 0:
            return 0, 1
        
        r = numerator / denominator
        
        # 简化的p值估计（实际应使用t分布）
        t_stat = r * math.sqrt((n - 2) / (1 - r**2)) if abs(r) < 1 else 0
        p_value = 0.05 if abs(t_stat) > 2 else 0.1  # 简化估计
        
        return r, p_value
    
    def correlation_analysis(self):
        """相关性分析"""
        print("正在进行相关性分析...")
        
        theoretical = [row['Theoretical_Value'] for row in self.data]
        exp_means = [row['Exp_Mean'] for row in self.data]
        control_means = [row['Control_Mean'] for row in self.data]
        
        # 实验组与理论值的相关性
        exp_r, exp_p = self.calculate_correlation(theoretical, exp_means)
        
        # 对照组与理论值的相关性
        ctrl_r, ctrl_p = self.calculate_correlation(theoretical, control_means)
        
        # 实验组与对照组的相关性
        inter_r, inter_p = self.calculate_correlation(exp_means, control_means)
        
        results = {
            'exp_vs_theoretical': {'r': exp_r, 'p': exp_p},
            'control_vs_theoretical': {'r': ctrl_r, 'p': ctrl_p},
            'exp_vs_control': {'r': inter_r, 'p': inter_p}
        }
        
        print("相关性分析完成！")
        return results
    
    def consistency_analysis(self):
        """一致性分析"""
        print("正在进行一致性分析...")
        
        # CV分析
        exp_cvs = [row['Exp_CV'] for row in self.data]
        control_cvs = [row['Control_CV'] for row in self.data]
        
        mean_exp_cv = sum(exp_cvs) / len(exp_cvs)
        mean_control_cv = sum(control_cvs) / len(control_cvs)
        
        # Bland-Altman分析
        differences = [row['Exp_Mean'] - row['Control_Mean'] for row in self.data]
        mean_diff = sum(differences) / len(differences)
        
        # 计算标准差
        var_diff = sum((d - mean_diff)**2 for d in differences) / len(differences)
        std_diff = math.sqrt(var_diff)
        
        results = {
            'mean_exp_cv': mean_exp_cv,
            'mean_control_cv': mean_control_cv,
            'bland_altman': {
                'mean_difference': mean_diff,
                'std_difference': std_diff,
                'upper_limit': mean_diff + 1.96 * std_diff,
                'lower_limit': mean_diff - 1.96 * std_diff
            }
        }
        
        print("一致性分析完成！")
        return results
    
    def statistical_tests(self):
        """简化的统计学检验"""
        print("正在进行统计学检验...")
        
        exp_means = [row['Exp_Mean'] for row in self.data]
        control_means = [row['Control_Mean'] for row in self.data]
        
        # 简化的配对t检验
        differences = [exp_means[i] - control_means[i] for i in range(len(exp_means))]
        mean_diff = sum(differences) / len(differences)
        
        # 计算t统计量（简化版）
        var_diff = sum((d - mean_diff)**2 for d in differences) / (len(differences) - 1)
        std_err = math.sqrt(var_diff / len(differences))
        t_stat = mean_diff / std_err if std_err > 0 else 0
        
        # 简化的p值估计
        p_value = 0.05 if abs(t_stat) > 2 else 0.1
        
        results = {
            'paired_t_test': {'statistic': t_stat, 'p_value': p_value},
            'mean_difference': mean_diff,
            'std_error': std_err
        }
        
        print("统计学检验完成！")
        return results
    
    def create_output_directory(self):
        """创建输出目录"""
        timestamp = datetime.now().strftime("%y%m%d%H%M")
        self.output_dir = f"outputs/{timestamp}"
        os.makedirs(self.output_dir, exist_ok=True)
        print(f"输出目录已创建: {self.output_dir}")
        return self.output_dir
    
    def save_data_csv(self):
        """保存数据到CSV文件"""
        if self.output_dir is None:
            self.create_output_directory()
        
        # 保存原始数据
        data_file = os.path.join(self.output_dir, "荧光强度检测原始数据.csv")
        
        if self.data:
            fieldnames = list(self.data[0].keys())
            with open(data_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(self.data)
        
        print("数据文件保存完成！")
    
    def print_summary(self, corr_results, consist_results, stats_results):
        """打印分析结果摘要"""
        print("\n" + "=" * 40)
        print("分析结果摘要")
        print("=" * 40)
        
        # 相关性结果
        print(f"\n【相关性分析】")
        print(f"实验组vs理论值: r={corr_results['exp_vs_theoretical']['r']:.3f}, "
              f"p={corr_results['exp_vs_theoretical']['p']:.3f}")
        print(f"对照组vs理论值: r={corr_results['control_vs_theoretical']['r']:.3f}, "
              f"p={corr_results['control_vs_theoretical']['p']:.3f}")
        print(f"实验组vs对照组: r={corr_results['exp_vs_control']['r']:.3f}, "
              f"p={corr_results['exp_vs_control']['p']:.3f}")
        
        # 一致性结果
        print(f"\n【一致性分析】")
        print(f"实验组平均CV: {consist_results['mean_exp_cv']:.2f}%")
        print(f"对照组平均CV: {consist_results['mean_control_cv']:.2f}%")
        print(f"Bland-Altman平均差值: {consist_results['bland_altman']['mean_difference']:.1f}")
        
        # 统计检验结果
        print(f"\n【统计检验】")
        print(f"配对t检验: t={stats_results['paired_t_test']['statistic']:.3f}, "
              f"p={stats_results['paired_t_test']['p_value']:.3f}")
        print(f"平均差值: {stats_results['mean_difference']:.1f}")
        
        # 检测效率摘要
        exp_effs = [row['Exp_Efficiency'] for row in self.data]
        ctrl_effs = [row['Control_Efficiency'] for row in self.data]
        
        print(f"\n【检测效率摘要】")
        print(f"实验组平均效率: {sum(exp_effs)/len(exp_effs):.1f}%")
        print(f"对照组平均效率: {sum(ctrl_effs)/len(ctrl_effs):.1f}%")
        print(f"效率提升: {sum(exp_effs)/len(exp_effs) - sum(ctrl_effs)/len(ctrl_effs):.1f}%")
    
    def run_demo_analysis(self):
        """运行演示分析"""
        print("=" * 60)
        print("荧光强度检测方法对比分析系统 - 简化演示版")
        print("=" * 60)
        print("注意：此为演示版本，完整功能请使用 fluorescence_analysis.py")
        print("=" * 60)
        
        # 生成数据
        self.generate_data()
        
        # 进行各项分析
        corr_results = self.correlation_analysis()
        consist_results = self.consistency_analysis()
        stats_results = self.statistical_tests()
        
        # 保存数据
        self.save_data_csv()
        
        # 打印结果摘要
        self.print_summary(corr_results, consist_results, stats_results)
        
        print(f"\n分析完成！结果已保存到: {self.output_dir}")
        print("=" * 60)
        print("\n要获得完整的可视化图表，请：")
        print("1. 安装依赖包：pip install numpy pandas matplotlib seaborn scipy")
        print("2. 运行完整版：python fluorescence_analysis.py")


def main():
    """主函数"""
    demo = SimpleFluorescenceDemo()
    demo.run_demo_analysis()
    return demo


if __name__ == "__main__":
    demo = main()
