#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
荧光强度检测分析系统测试脚本
Test Script for Fluorescence Intensity Detection Analysis System

此脚本用于验证主程序的各项功能是否正常工作
在确认所有测试通过后，此测试专用代码可被安全删除，以保持项目结构的整洁。
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import shutil

# 导入主程序
from fluorescence_analysis import FluorescenceAnalyzer

def test_data_generation():
    """测试数据生成功能"""
    print("测试1: 数据生成功能...")
    
    analyzer = FluorescenceAnalyzer()
    data = analyzer.generate_data()
    
    # 验证数据结构
    assert isinstance(data, pd.DataFrame), "数据应为DataFrame格式"
    assert len(data) == 4, "应生成4行数据（对应4个浓度水平）"
    
    # 验证列名
    expected_columns = ['Concentration_Level', 'Theoretical_Value', 
                       'F_1A1', 'F_1A2', 'F_1A3', 'F_1B1', 'F_1B2', 'F_1B3',
                       'Exp_Mean', 'Control_Mean', 'Exp_CV', 'Control_CV',
                       'Exp_Efficiency', 'Control_Efficiency']
    for col in expected_columns:
        assert col in data.columns, f"缺少列: {col}"
    
    # 验证理论值
    expected_theoretical = [8000, 32000, 128000, 256000]
    assert list(data['Theoretical_Value']) == expected_theoretical, "理论值不正确"
    
    # 验证效率范围
    assert all(data['Exp_Efficiency'] >= 50) and all(data['Exp_Efficiency'] <= 80), \
        "实验组效率应在50-80%范围内"
    assert all(data['Control_Efficiency'] >= 30) and all(data['Control_Efficiency'] <= 60), \
        "对照组效率应在30-60%范围内"
    
    # 验证数值为正
    numeric_columns = ['F_1A1', 'F_1A2', 'F_1A3', 'F_1B1', 'F_1B2', 'F_1B3',
                      'Exp_Mean', 'Control_Mean']
    for col in numeric_columns:
        assert all(data[col] >= 0), f"列{col}应包含非负值"
    
    print("✓ 数据生成功能测试通过")
    return analyzer

def test_correlation_analysis(analyzer):
    """测试相关性分析功能"""
    print("测试2: 相关性分析功能...")
    
    results = analyzer.correlation_analysis()
    
    # 验证结果结构
    assert 'exp_vs_theoretical' in results, "缺少实验组vs理论值相关性"
    assert 'control_vs_theoretical' in results, "缺少对照组vs理论值相关性"
    assert 'exp_vs_control' in results, "缺少实验组vs对照组相关性"
    
    # 验证相关系数范围
    for key in results:
        assert -1 <= results[key]['pearson_r'] <= 1, f"{key}的Pearson相关系数超出范围"
        assert -1 <= results[key]['spearman_r'] <= 1, f"{key}的Spearman相关系数超出范围"
        assert 0 <= results[key]['pearson_p'] <= 1, f"{key}的Pearson p值超出范围"
        assert 0 <= results[key]['spearman_p'] <= 1, f"{key}的Spearman p值超出范围"
    
    # 验证实验组相关性应优于对照组
    exp_corr = results['exp_vs_theoretical']['pearson_r']
    ctrl_corr = results['control_vs_theoretical']['pearson_r']
    assert exp_corr >= ctrl_corr, "实验组与理论值的相关性应不低于对照组"
    
    print("✓ 相关性分析功能测试通过")

def test_consistency_analysis(analyzer):
    """测试一致性分析功能"""
    print("测试3: 一致性分析功能...")
    
    results = analyzer.consistency_analysis()
    
    # 验证结果结构
    assert 'mean_exp_cv' in results, "缺少实验组平均CV"
    assert 'mean_control_cv' in results, "缺少对照组平均CV"
    assert 'bland_altman' in results, "缺少Bland-Altman分析"
    
    # 验证CV值为正
    assert results['mean_exp_cv'] >= 0, "实验组CV应为非负值"
    assert results['mean_control_cv'] >= 0, "对照组CV应为非负值"
    
    # 验证Bland-Altman结果
    ba = results['bland_altman']
    assert 'mean_difference' in ba, "缺少平均差值"
    assert 'std_difference' in ba, "缺少差值标准差"
    assert 'upper_limit' in ba, "缺少上限"
    assert 'lower_limit' in ba, "缺少下限"
    
    # 验证上下限关系
    assert ba['upper_limit'] > ba['lower_limit'], "上限应大于下限"
    
    print("✓ 一致性分析功能测试通过")

def test_statistical_tests(analyzer):
    """测试统计学检验功能"""
    print("测试4: 统计学检验功能...")
    
    results = analyzer.statistical_tests()
    
    # 验证结果结构
    assert 'paired_t_test' in results, "缺少配对t检验"
    assert 'wilcoxon_test' in results, "缺少Wilcoxon检验"
    assert 'efficiency_t_test' in results, "缺少效率t检验"
    
    # 验证p值范围
    for test_name in results:
        assert 0 <= results[test_name]['p_value'] <= 1, f"{test_name}的p值超出范围"
    
    print("✓ 统计学检验功能测试通过")

def test_output_generation(analyzer):
    """测试输出生成功能"""
    print("测试5: 输出生成功能...")
    
    # 创建输出目录
    output_dir = analyzer.create_output_directory()
    assert os.path.exists(output_dir), "输出目录创建失败"
    
    # 测试数据保存
    analyzer.save_data()
    
    # 验证CSV文件
    data_file = os.path.join(output_dir, "荧光强度检测原始数据.csv")
    results_file = os.path.join(output_dir, "统计分析结果.csv")
    
    assert os.path.exists(data_file), "原始数据文件未生成"
    assert os.path.exists(results_file), "分析结果文件未生成"
    
    # 验证文件内容
    data_df = pd.read_csv(data_file, encoding='utf-8-sig')
    results_df = pd.read_csv(results_file, encoding='utf-8-sig')
    
    assert len(data_df) == 4, "原始数据文件行数不正确"
    assert len(results_df) > 0, "分析结果文件为空"
    
    print("✓ 输出生成功能测试通过")
    
    return output_dir

def test_visualization_generation(analyzer):
    """测试可视化生成功能"""
    print("测试6: 可视化生成功能...")
    
    # 生成所有图表
    analyzer.plot_scatter_comparison()
    analyzer.plot_boxplot_comparison()
    analyzer.plot_bland_altman()
    analyzer.plot_correlation_heatmap()
    analyzer.plot_efficiency_comparison()
    
    # 验证图片文件
    expected_plots = [
        "实际值vs理论值散点图对比.png",
        "荧光强度分布箱线图.png",
        "Bland-Altman一致性分析图.png",
        "相关性热图.png",
        "检测效率对比柱状图.png"
    ]
    
    for plot_name in expected_plots:
        plot_path = os.path.join(analyzer.output_dir, plot_name)
        assert os.path.exists(plot_path), f"图片文件未生成: {plot_name}"
        
        # 验证文件大小（确保不是空文件）
        assert os.path.getsize(plot_path) > 1000, f"图片文件可能损坏: {plot_name}"
    
    print("✓ 可视化生成功能测试通过")

def test_complete_workflow():
    """测试完整工作流程"""
    print("测试7: 完整工作流程...")
    
    analyzer = FluorescenceAnalyzer()
    
    # 运行完整分析（但不打印详细输出）
    import io
    import contextlib
    
    # 捕获输出以避免测试时的冗余信息
    f = io.StringIO()
    with contextlib.redirect_stdout(f):
        analyzer.run_complete_analysis()
    
    # 验证所有结果都已生成
    assert analyzer.data is not None, "数据未生成"
    assert 'correlation' in analyzer.results, "相关性分析未完成"
    assert 'consistency' in analyzer.results, "一致性分析未完成"
    assert 'statistical_tests' in analyzer.results, "统计检验未完成"
    assert analyzer.output_dir is not None, "输出目录未创建"
    
    print("✓ 完整工作流程测试通过")
    
    return analyzer.output_dir

def cleanup_test_outputs():
    """清理测试产生的输出文件"""
    print("清理测试文件...")
    
    if os.path.exists("outputs"):
        # 只删除测试期间创建的目录
        for item in os.listdir("outputs"):
            item_path = os.path.join("outputs", item)
            if os.path.isdir(item_path):
                # 检查是否是测试期间创建的（最近5分钟内）
                creation_time = os.path.getctime(item_path)
                current_time = datetime.now().timestamp()
                if current_time - creation_time < 300:  # 5分钟内
                    shutil.rmtree(item_path)
                    print(f"已删除测试目录: {item_path}")

def run_all_tests():
    """运行所有测试"""
    print("开始运行荧光强度检测分析系统测试...")
    print("=" * 60)
    
    try:
        # 测试1: 数据生成
        analyzer = test_data_generation()
        
        # 测试2-4: 各项分析功能
        test_correlation_analysis(analyzer)
        test_consistency_analysis(analyzer)
        test_statistical_tests(analyzer)
        
        # 测试5-6: 输出和可视化
        output_dir = test_output_generation(analyzer)
        test_visualization_generation(analyzer)
        
        # 测试7: 完整工作流程
        test_output_dir = test_complete_workflow()
        
        print("=" * 60)
        print("🎉 所有测试通过！")
        print("=" * 60)
        print("主程序功能验证完成，可以安全使用。")
        print(f"测试输出目录: {test_output_dir}")
        
        # 询问是否清理测试文件
        response = input("\n是否清理测试产生的文件？(y/n): ").lower().strip()
        if response == 'y':
            cleanup_test_outputs()
            print("测试文件清理完成。")
        
        return True
        
    except AssertionError as e:
        print(f"❌ 测试失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
